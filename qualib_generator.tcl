# Qualib Generator - 自动执行 qualib 操作
# 测试变量设置（实际使用时这些变量会由外部提供）
array set qualib {}
set qualib(workspace) "/projects/taishan/analog/work/chenmiaomiao/VerificationData/qualib/202503201431"
set qualib(session) "run_202503201431"
set qualib(verilog) "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/verilog/pma_s1t4r4.lvs.v.gz"
set qualib(cdl) "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/cdl/pma_s1t4r4.spi"
set qualib(gds) "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/gds/pma_s1t4r4_FullPnR.gds"
set qualib(tech_gds) {
    "/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.layermap"
    "/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.tf"
}
set qualib(lef) {
    "/tech_1/designkit/tsmc/n12ffcll/pdk/PNR/tf_innovus/latest/ori/tn12clpr001e1_1_2a/PRTF_Innovus_12nm_001_Cad_V12a/PRTF_Innovus_12nm_001_Cad_V12a/PR_tech/Cadence/LefHeader/Standard/VHV/PRTF_Innovus_N12_11M_2Xa1Xd3Xe2Y2R_UTRDL_H384_CPODE.12a.tlef"
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lef/pma_s1t4r4.lef.gz"
}
set qualib(tmlib) {
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_tt_0p8v_25c_typical_SETUP.merge.lib"
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_m40c_rcworst_HOLD.merge.lib"
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_m40c_rcbest_HOLD.merge.lib"
}
set qualib(corners) {
    "tt_0p8v_25c_typical_SETUP.merge"
    "ssgnp_0p72v_m40c_rcworst_HOLD.merge"
    "ffgnp_0p88v_m40c_rcbest_HOLD.merge"
}

# 辅助函数：从文件路径提取 corner 名称
proc extract_corner_name {lib_path} {
    set filename [file tail $lib_path]
    if {[regexp {_([^_]+\.merge)\.lib} $filename match corner]} {
        return $corner
    } elseif {[regexp {_([^_]+)\.lib} $filename match corner]} {
        return $corner
    } else {
        return [file rootname $filename]
    }
}

# 创建 workspace
if {[info exists qualib(workspace)] && $qualib(workspace) ne ""} {
    create_workspace -overwrite $qualib(workspace)
}

# 创建 session
if {[info exists qualib(session)] && $qualib(session) ne ""} {
    create_session $qualib(session) -vendor [exec whoami] -version 1.0
}

# Link Verilog
if {[info exists qualib(verilog)] && $qualib(verilog) ne "" && [file exists $qualib(verilog)]} {
    link_verilog -session $qualib(session) $qualib(verilog)
}

# Link CDL
if {[info exists qualib(cdl)] && $qualib(cdl) ne "" && [file exists $qualib(cdl)]} {
    link_cdl -session $qualib(session) $qualib(cdl)
}

# Link GDS tech files
if {[info exists qualib(tech_gds)] && [llength $qualib(tech_gds)] > 0} {
    foreach tech_file $qualib(tech_gds) {
        if {[file exists $tech_file]} {
            link_gds -session $qualib(session) -tech $tech_file
        }
    }
}

# Link GDS
if {[info exists qualib(gds)] && $qualib(gds) ne "" && [file exists $qualib(gds)]} {
    link_gds -session $qualib(session) $qualib(gds)
}

# Link LEF
if {[info exists qualib(lef)]} {
    if {[llength $qualib(lef)] > 1} {
        foreach lef_file $qualib(lef) {
            if {[file exists $lef_file]} {
                link_lef -session $qualib(session) $lef_file
            }
        }
    } elseif {$qualib(lef) ne "" && [file exists $qualib(lef)]} {
        link_lef -session $qualib(session) $qualib(lef)
    }
}

# Process timing libraries
if {[info exists qualib(tmlib)] && [llength $qualib(tmlib)] > 0} {
    if {[info exists qualib(corners)] && [llength $qualib(corners)] > 0} {
        # 使用提供的 corner 名称
        for {set i 0} {$i < [llength $qualib(tmlib)]} {incr i} {
            set lib_file [lindex $qualib(tmlib) $i]
            if {$i < [llength $qualib(corners)]} {
                set corner_name [lindex $qualib(corners) $i]
            } else {
                set corner_name [extract_corner_name $lib_file]
            }

            if {[file exists $lib_file]} {
                create_tmlib_corner -session $qualib(session) $corner_name
                link_tmlib -session $qualib(session) -corner $corner_name $lib_file
            }
        }
    } else {
        # 从文件名自动提取 corner 名称
        foreach lib_file $qualib(tmlib) {
            set corner_name [extract_corner_name $lib_file]
            if {[file exists $lib_file]} {
                create_tmlib_corner -session $qualib(session) $corner_name
                link_tmlib -session $qualib(session) -corner $corner_name $lib_file
            }
        }
    }
}

# 添加规则文件（如果存在）
if {[info exists qualib(rules)] && $qualib(rules) ne "" && [file exists $qualib(rules)]} {
    source $qualib(rules)
}

# 检查和启动
inspect -force -session $qualib(session)

# 启动 GUI（如果需要）
if {[info exists qualib(start_gui)] && $qualib(start_gui)} {
    start_gui
}
    "tt_0p8v_25c_typical_SETUP.merge"
    "ssgnp_0p72v_m40c_rcworst_HOLD.merge"
    "ffgnp_0p88v_m40c_rcbest_HOLD.merge"
}

# 辅助函数：检查文件是否存在
proc check_file_exists {filepath} {
    if {[file exists $filepath]} {
        return 1
    } else {
        puts "Warning: File does not exist: $filepath"
        return 0
    }
}

# 辅助函数：从文件路径提取 corner 名称
proc extract_corner_name {lib_path} {
    set filename [file tail $lib_path]
    # 移除文件扩展名
    set basename [file rootname $filename]
    # 提取 corner 名称（假设格式为 pma_s1t4r4_CORNER_NAME.lib）
    if {[regexp {_([^_]+)\.merge\.lib$} $filename match corner]} {
        return $corner
    } elseif {[regexp {_([^_]+)\.lib$} $filename match corner]} {
        return $corner
    } else {
        # 如果无法提取，使用文件名作为 corner 名称
        return $basename
    }
}

# 主函数：生成 qualib 脚本
proc generate_qualib_script {} {
    global qualib
    
    puts "# Generated Qualib Script"
    puts "# Date: [clock format [clock seconds]]"
    puts ""
    
    # 创建 workspace
    if {$qualib(workspace) ne ""} {
        puts "create_workspace -overwrite $qualib(workspace)"
    } else {
        puts "# Warning: No workspace specified"
    }
    
    # 创建 session
    if {$qualib(session) ne ""} {
        puts "create_session $qualib(session) -vendor [exec whoami] -version 1.0"
    } else {
        puts "# Warning: No session specified"
    }
    
    puts ""
    
    # Link Verilog
    if {$qualib(verilog) ne "" && [check_file_exists $qualib(verilog)]} {
        puts "# Link Verilog"
        puts "link_verilog -session {$qualib(session)} {$qualib(verilog)}"
        puts ""
    }
    
    # Link CDL
    if {$qualib(cdl) ne "" && [check_file_exists $qualib(cdl)]} {
        puts "# Link CDL"
        puts "link_cdl -session {$qualib(session)} {$qualib(cdl)}"
        puts ""
    }
    
    # Link GDS tech files
    if {[llength $qualib(tech_gds)] > 0} {
        puts "# Link GDS tech files"
        foreach tech_file $qualib(tech_gds) {
            if {[check_file_exists $tech_file]} {
                puts "link_gds -session {$qualib(session)} -tech {$tech_file}"
            }
        }
        puts ""
    }
    
    # Link GDS
    if {$qualib(gds) ne "" && [check_file_exists $qualib(gds)]} {
        puts "# Link GDS"
        puts "link_gds -session {$qualib(session)} {$qualib(gds)}"
        puts ""
    }
    
    # Link LEF
    if {$qualib(lef) ne "" && [check_file_exists $qualib(lef)]} {
        puts "# Link LEF"
        puts "link_lef -session {$qualib(session)} {$qualib(lef)}"
        puts ""
    }
    
    # Process timing libraries
    if {[llength $qualib(tmlib)] > 0} {
        puts "# Create timing library corners and link libraries"
        
        # 如果提供了 corners 列表，使用它；否则从文件名提取
        if {[llength $qualib(corners)] > 0} {
            # 使用提供的 corner 名称
            if {[llength $qualib(corners)] != [llength $qualib(tmlib)]} {
                puts "# Warning: Number of corners doesn't match number of libraries"
            }
            
            for {set i 0} {$i < [llength $qualib(tmlib)]} {incr i} {
                set lib_file [lindex $qualib(tmlib) $i]
                if {$i < [llength $qualib(corners)]} {
                    set corner_name [lindex $qualib(corners) $i]
                } else {
                    set corner_name [extract_corner_name $lib_file]
                }
                
                if {[check_file_exists $lib_file]} {
                    puts "create_tmlib_corner -session {$qualib(session)} {$corner_name}"
                    puts "link_tmlib -session {$qualib(session)} -corner {$corner_name} {$lib_file}"
                }
            }
        } else {
            # 从文件名自动提取 corner 名称
            foreach lib_file $qualib(tmlib) {
                set corner_name [extract_corner_name $lib_file]
                
                if {[check_file_exists $lib_file]} {
                    puts "create_tmlib_corner -session {$qualib(session)} {$corner_name}"
                    puts "link_tmlib -session {$qualib(session)} -corner {$corner_name} {$lib_file}"
                }
            }
        }
        puts ""
    }
    
    # 添加检查和启动 GUI
    puts "# Inspection and GUI"
    puts "inspect -force -session $qualib(session)"
    puts "start_gui"
}

# 设置变量的函数
proc set_qualib_var {var_name value} {
    global qualib
    set qualib($var_name) $value
    puts "Set qualib($var_name) = $value"
}

# 添加到列表变量的函数
proc add_to_qualib_list {var_name value} {
    global qualib
    lappend qualib($var_name) $value
    puts "Added to qualib($var_name): $value"
}

# 显示当前配置的函数
proc show_qualib_config {} {
    global qualib
    puts "Current Qualib Configuration:"
    puts "=============================="
    foreach {key value} [array get qualib] {
        if {[llength $value] > 1} {
            puts "$key: [join $value {, }]"
        } else {
            puts "$key: $value"
        }
    }
    puts "=============================="
}

# 示例使用函数
proc example_usage {} {
    global qualib
    
    puts "Example Usage:"
    puts "=============="
    puts ""
    puts "# 设置基本信息"
    puts "set_qualib_var workspace \"/projects/workspace/my_project\""
    puts "set_qualib_var session \"run_[clock format [clock seconds] -format %Y%m%d%H%M]\""
    puts ""
    puts "# 设置文件路径"
    puts "set_qualib_var verilog \"/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/verilog/pma_s1t4r4.lvs.v.gz\""
    puts "set_qualib_var cdl \"/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/cdl/pma_s1t4r4.spi\""
    puts "set_qualib_var gds \"/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/gds/pma_s1t4r4_FullPnR.gds\""
    puts ""
    puts "# 添加技术文件"
    puts "add_to_qualib_list tech_gds \"/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.layermap\""
    puts "add_to_qualib_list tech_gds \"/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.tf\""
    puts ""
    puts "# 添加时序库"
    puts "add_to_qualib_list tmlib \"/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_tt_0p8v_25c_typical_SETUP.merge.lib\""
    puts "add_to_qualib_list corners \"tt_0p8v_25c_typical_SETUP.merge\""
    puts ""
    puts "# 生成脚本"
    puts "generate_qualib_script"
}

# 主程序入口
if {[info exists argv0] && $argv0 eq [info script]} {
    puts "Qualib Generator Tool"
    puts "===================="
    puts ""
    
    if {[llength $argv] == 0} {
        example_usage
    } else {
        # 处理命令行参数
        foreach arg $argv {
            switch -exact $arg {
                "-help" -
                "--help" -
                "-h" {
                    example_usage
                    exit 0
                }
                "-example" {
                    example_usage
                    exit 0
                }
                default {
                    puts "Unknown argument: $arg"
                    puts "Use -help for usage information"
                    exit 1
                }
            }
        }
    }
}
