# Qualib 执行脚本 - 直接在 qualib 中执行
# 变量设置（实际使用时修改这些变量值）
array set qualib {}
set qualib(workspace) "/projects/taishan/analog/work/chenmiaomiao/VerificationData/qualib/202503201431"
set qualib(session) "run_202503201431"
set qualib(verilog) "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/verilog/pma_s1t4r4.lvs.v.gz"
set qualib(cdl) "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/cdl/pma_s1t4r4.spi"
set qualib(gds) "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/gds/pma_s1t4r4_FullPnR.gds"
set qualib(tech_gds) {
    "/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.layermap"
    "/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.tf"
}
set qualib(lef) {
    "/tech_1/designkit/tsmc/n12ffcll/pdk/PNR/tf_innovus/latest/ori/tn12clpr001e1_1_2a/PRTF_Innovus_12nm_001_Cad_V12a/PRTF_Innovus_12nm_001_Cad_V12a/PR_tech/Cadence/LefHeader/Standard/VHV/PRTF_Innovus_N12_11M_2Xa1Xd3Xe2Y2R_UTRDL_H384_CPODE.12a.tlef"
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lef/pma_s1t4r4.lef.gz"
}
set qualib(tmlib) {
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_tt_0p8v_25c_typical_SETUP.merge.lib"
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_m40c_rcworst_HOLD.merge.lib"
    "/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_m40c_rcbest_HOLD.merge.lib"
}
set qualib(corners) {
    "tt_0p8v_25c_typical_SETUP.merge"
    "ssgnp_0p72v_m40c_rcworst_HOLD.merge"
    "ffgnp_0p88v_m40c_rcbest_HOLD.merge"
}

# 创建 workspace
if {[info exists qualib(workspace)] && $qualib(workspace) ne ""} {
    create_workspace -overwrite $qualib(workspace)
}

# 创建 session
if {[info exists qualib(session)] && $qualib(session) ne ""} {
    create_session $qualib(session) -vendor [exec whoami] -version 1.0
}

# Link Verilog
if {[info exists qualib(verilog)] && $qualib(verilog) ne "" && [file exists $qualib(verilog)]} {
    link_verilog -session $qualib(session) $qualib(verilog)
}

# Link CDL
if {[info exists qualib(cdl)] && $qualib(cdl) ne "" && [file exists $qualib(cdl)]} {
    link_cdl -session $qualib(session) $qualib(cdl)
}

# Link GDS tech files
if {[info exists qualib(tech_gds)] && [llength $qualib(tech_gds)] > 0} {
    foreach tech_file $qualib(tech_gds) {
        if {[file exists $tech_file]} {
            link_gds -session $qualib(session) -tech $tech_file
        }
    }
}

# Link GDS
if {[info exists qualib(gds)] && $qualib(gds) ne "" && [file exists $qualib(gds)]} {
    link_gds -session $qualib(session) $qualib(gds)
}

# Link LEF
if {[info exists qualib(lef)]} {
    if {[llength $qualib(lef)] > 1} {
        foreach lef_file $qualib(lef) {
            if {[file exists $lef_file]} {
                link_lef -session $qualib(session) $lef_file
            }
        }
    } elseif {$qualib(lef) ne "" && [file exists $qualib(lef)]} {
        link_lef -session $qualib(session) $qualib(lef)
    }
}

# Process timing libraries
if {[info exists qualib(tmlib)] && [llength $qualib(tmlib)] > 0} {
    for {set i 0} {$i < [llength $qualib(tmlib)]} {incr i} {
        set lib_file [lindex $qualib(tmlib) $i]
        set corner_name [lindex $qualib(corners) $i]

        if {[file exists $lib_file]} {
            create_tmlib_corner -session $qualib(session) $corner_name
            link_tmlib -session $qualib(session) -corner $corner_name $lib_file
        }
    }
}

# 添加规则文件（如果存在）
if {[info exists qualib(rules)] && $qualib(rules) ne "" && [file exists $qualib(rules)]} {
    source $qualib(rules)
}

# 检查和启动
inspect -force -session $qualib(session)

# 启动 GUI（如果需要）
if {[info exists qualib(start_gui)] && $qualib(start_gui)} {
    start_gui
}

