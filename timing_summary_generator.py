#!/usr/bin/env python3
"""
Timing Summary Report Generator
自动扫描 sp_timing 目录下的所有 view，生成 timing summary 报告
"""

import os
import re
import glob
from pathlib import Path

def extract_min_max_delay(file_path):
    """从报告文件中提取 Min element delay 和 Max element delay"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        # 获取最后两行
        if len(lines) >= 2:
            last_two_lines = lines[-2:]
            min_delay = None
            max_delay = None

            for line in last_two_lines:
                line = line.strip()
                if 'Min element delay' in line:
                    # 提取数值，支持冒号和空格分隔，更宽松的匹配
                    match = re.search(r'Min element delay\s*:\s*([0-9.]+)', line)
                    if match:
                        min_delay = match.group(1)
                elif 'Max element delay' in line:
                    # 提取数值，支持冒号和空格分隔，更宽松的匹配
                    match = re.search(r'Max element delay\s*:\s*([0-9.]+)', line)
                    if match:
                        max_delay = match.group(1)

            return min_delay, max_delay
    except Exception as e:
        print(f"Error reading {file_path}: {e}")

    return None, None

def extract_violations(file_path):
    """从报告文件中提取所有 VIOLATION 开头的行"""
    violations = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('VIOLATION'):
                    violations.append(line)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return violations

def scan_sp_timing_directory(sp_timing_dir):
    """扫描 sp_timing 目录，获取所有 view"""
    views = []
    if os.path.exists(sp_timing_dir):
        for item in os.listdir(sp_timing_dir):
            item_path = os.path.join(sp_timing_dir, item)
            if os.path.isdir(item_path):
                reports_dir = os.path.join(item_path, 'reports')
                if os.path.exists(reports_dir):
                    views.append(item)
    return sorted(views)

def get_delay_reports(reports_dir):
    """获取需要提取 Min/Max delay 的报告文件"""
    delay_reports = []
    
    # 定义需要处理的报告类型
    report_patterns = [
        'dll_phy_top.read_adder_delay_*.rpt.ets',
        'dll_phy_top.master_phase_*_delay.rpt.ets',
        'dll_phy_top.read_delay.rpt.ets',
        'dll_phy_top.write_adder_delay_*.rpt.ets',
        'dll_phy_top.write_delay.rpt.ets',
        'dll_phy_top.write_dqs_delay.rpt.ets'
    ]
    
    for pattern in report_patterns:
        files = glob.glob(os.path.join(reports_dir, pattern))
        delay_reports.extend(files)
    
    return sorted(delay_reports)

def get_violation_reports(reports_dir):
    """获取需要提取 VIOLATION 的报告文件"""
    all_files = glob.glob(os.path.join(reports_dir, '*.rpt.ets'))
    delay_files = get_delay_reports(reports_dir)
    
    # 排除已经在 delay_reports 中的文件
    violation_reports = [f for f in all_files if f not in delay_files]
    
    return sorted(violation_reports)

def generate_timing_summary(sp_timing_dir='sp_timing', output_file='sp_timing_summary.rpt'):
    """生成 timing summary 报告"""
    
    # 扫描所有 view
    views = scan_sp_timing_directory(sp_timing_dir)
    
    if not views:
        print(f"No views found in {sp_timing_dir}")
        return
    
    print(f"Found {len(views)} views: {', '.join(views)}")
    
    # 收集所有需要处理的报告文件
    all_delay_reports = set()
    all_violation_reports = set()
    
    for view in views:
        reports_dir = os.path.join(sp_timing_dir, view, 'reports')
        delay_reports = get_delay_reports(reports_dir)
        violation_reports = get_violation_reports(reports_dir)
        
        # 提取文件名（不包含路径）
        for report in delay_reports:
            all_delay_reports.add(os.path.basename(report))
        
        for report in violation_reports:
            all_violation_reports.add(os.path.basename(report))
    
    # 生成报告
    with open(output_file, 'w') as f:
        # 处理 delay 报告
        for report_name in sorted(all_delay_reports):
            f.write("###########################################  \n")
            f.write(f"{report_name}                               \n")
            f.write("###########################################  \n")
            f.write("View                                         Min element delay       Max element delay    path\n")
            
            for view in views:
                report_path = os.path.join(sp_timing_dir, view, 'reports', report_name)
                if os.path.exists(report_path):
                    # 检查文件是否为空
                    if os.path.getsize(report_path) == 0:
                        f.write(f"{view:<40} {'Empty File':<23} {'Empty File':<20} {report_path}\n")
                    else:
                        min_delay, max_delay = extract_min_max_delay(report_path)
                        if min_delay and max_delay:
                            f.write(f"{view:<40} {min_delay:<23} {max_delay:<20} {report_path}\n")
                        else:
                            f.write(f"{view:<40} {'Parse Error':<23} {'Parse Error':<20} {report_path}\n")
                else:
                    f.write(f"{view:<40} {'File Not Found':<23} {'File Not Found':<20} {report_path}\n")
        
        # 处理 violation 报告
        for report_name in sorted(all_violation_reports):
            f.write("###########################################  \n")
            f.write(f"{report_name}                             \n")
            f.write("###########################################  \n")
            
            for view in views:
                report_path = os.path.join(sp_timing_dir, view, 'reports', report_name)
                if os.path.exists(report_path):
                    violations = extract_violations(report_path)
                    f.write(f"View  {view}     \n")
                    f.write(f"path  {report_path}\n")
                    
                    if violations:
                        for violation in violations:
                            f.write(f"{violation}\n")
                    else:
                        f.write("No violations found\n")
    
    print(f"Timing summary report generated: {output_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Generate timing summary report')
    parser.add_argument('-d', '--directory', default='sp_timing', 
                       help='SP timing directory (default: sp_timing)')
    parser.add_argument('-o', '--output', default='sp_timing_summary.rpt',
                       help='Output report file (default: sp_timing_summary.rpt)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        print(f"Scanning directory: {args.directory}")
        print(f"Output file: {args.output}")
    
    generate_timing_summary(args.directory, args.output)

if __name__ == "__main__":
    main()
