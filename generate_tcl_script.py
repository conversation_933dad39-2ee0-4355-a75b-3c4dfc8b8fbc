#!/usr/bin/env python3
"""
Generate TCL scripts for Innovus flow
"""
import os
import re
import sys

def extract_usage_command(tcl_file_path):
    """Extract the USAGE command from a TCL file."""
    try:
        with open(tcl_file_path, 'r') as f:
            first_line = f.readline().strip()

        # Find the USAGE line
        usage_match = re.search(r'#USAGE:(.*?)(\n|$)', first_line)
        if usage_match:
            return usage_match.group(1).strip()
        return None
    except Exception as e:
        print(f"Error reading {tcl_file_path}: {e}")
        return None

def parse_yaml_file(file_path):
    """Simple YAML parser for our specific format."""
    stages = {}
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()

        current_stage = None
        for line in lines:
            line = line.strip()
            if line.startswith('stepList:'):
                continue
            elif line.startswith('innovus.'):
                # New stage found
                current_stage = line.rstrip(':')
                stages[current_stage] = []
            elif current_stage and line.startswith('-'):
                # Proc name for the current stage
                proc_name = line.replace('-', '').strip()
                if proc_name:
                    stages[current_stage].append(proc_name)
    except Exception as e:
        print(f"Error parsing YAML file {file_path}: {e}")

    return stages

def generate_tcl_scripts(template_dir='template', flow_dir='flowRoot', output_dir='output', verbose=False):
    """
    Generate TCL scripts for all stages in step_list.yaml

    Args:
        template_dir (str): Directory containing templates
        flow_dir (str): Directory containing flow files
        output_dir (str): Directory to write output files
        verbose (bool): Enable verbose output

    Returns:
        bool: True if successful, False otherwise
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Read step_list.yaml
    yaml_path = os.path.join(template_dir, 'step_list.yaml')
    if verbose:
        print(f"Reading YAML file: {yaml_path}")

    stages = parse_yaml_file(yaml_path)

    if not stages:
        print(f"No stages found in {yaml_path}")
        return False

    if verbose:
        print(f"Found {len(stages)} stage(s): {', '.join(stages.keys())}")

    # Generate step_list.tcl content
    step_list_content = "# Define step list\nset stepList {\n"

    # Process each stage
    for stage, proc_names in stages.items():
        stage_name = stage  # e.g., "innovus.place"
        step_list_content += f"    {stage_name} {{\n"

        # Process each proc in this stage
        for proc_name in proc_names:
            # Find the corresponding TCL file
            tcl_file_path = os.path.join(flow_dir, 'Common', 'STEP', 'innovus', f"{proc_name.replace('innovus.', '')}.tcl")

            if os.path.exists(tcl_file_path):
                usage_command = extract_usage_command(tcl_file_path)
                if usage_command:
                    step_list_content += f'        "{usage_command}"\n'
                    if verbose:
                        print(f"Added command for {stage_name}: {usage_command}")
                else:
                    print(f"Warning: No USAGE command found in {tcl_file_path}")
            else:
                print(f"Warning: TCL file not found: {tcl_file_path}")

        step_list_content += "    }\n"

    step_list_content += "}"

    # Write step_list.tcl
    step_list_path = os.path.join(output_dir, 'step_list.tcl')
    with open(step_list_path, 'w') as f:
        f.write(step_list_content)

    if verbose:
        print(f"Generated {step_list_path}")

    # Get all unique proc names across all stages
    all_proc_names = set()
    for proc_names in stages.values():
        all_proc_names.update(proc_names)

    # Create a list of source commands for all proc files
    source_commands = []
    for proc_name in all_proc_names:
        proc_file = proc_name.replace('innovus.', '')
        tcl_path = os.path.join(flow_dir, 'Common', 'STEP', 'innovus', f"{proc_file}.tcl")
        # Use relative path for better portability
        rel_path = os.path.relpath(tcl_path, output_dir)
        source_commands.append(f'source "{rel_path}"')

    # Check if var.tcl exists in the current working directory
    cwd = os.getcwd()
    var_tcl_path = os.path.join(cwd, 'var.tcl')
    var_tcl_exists = os.path.exists(var_tcl_path)

    if var_tcl_exists and verbose:
        print(f"Found var.tcl in current directory: {var_tcl_path}")

    # Generate TCL file for each stage
    generated_files = [step_list_path]

    for stage in stages.keys():
        # Get the stage name without "innovus." prefix for the template and output file
        stage_short = stage.replace('innovus.', '')

        # Read the template file
        template_path = os.path.join(template_dir, f'{stage_short}_template')
        if not os.path.exists(template_path) and stage_short != 'place':
            # If template doesn't exist, try to use place_template as a fallback
            template_path = os.path.join(template_dir, 'place_template')
            if verbose:
                print(f"Template for {stage_short} not found, using place_template as fallback")

        try:
            with open(template_path, 'r') as f:
                template_content = f.read()
            if verbose:
                print(f"Read template file: {template_path}")
        except Exception as e:
            print(f"Error reading template file {template_path}: {e}")
            continue

        # Create the TCL file content
        final_content = f"""# Generated TCL script for {stage}
source {os.path.relpath(step_list_path, output_dir)}

# Source proc files
"""

        # Add source commands for all proc files
        final_content += "\n".join(source_commands) + "\n"

        # Add the template content
        final_content += f"\n# {stage_short.capitalize()} procedure\n"

        # Modify the template content to add var.tcl source command if needed
        if var_tcl_exists:
            # Find the position to insert the source command
            # We need to find the first line after "proc innovus.XXX {args} {"
            proc_pattern = r'proc\s+innovus\.' + stage_short + r'\s*\{\s*args\s*\}\s*\{'
            match = re.search(proc_pattern, template_content)

            if match:
                # Find the end of the matched line
                match_end = match.end()
                # Find the position of the next newline after the match
                next_newline = template_content.find('\n', match_end)

                if next_newline != -1:
                    # Insert the source command after the proc line
                    modified_template = (
                        template_content[:next_newline+1] +
                        f'    source "{os.path.abspath(var_tcl_path)}"\n' +
                        template_content[next_newline+1:]
                    )
                    final_content += modified_template
                else:
                    # If there's no newline after the proc line, just append the source command
                    final_content += template_content + f'\n    source "{os.path.abspath(var_tcl_path)}"\n'
            else:
                # If we can't find the proc line, just add the template content as is
                if verbose:
                    print(f"Warning: Could not find 'proc innovus.{stage_short} {{args}} {{' in template")
                final_content += template_content
        else:
            final_content += template_content

        # Write the final TCL file
        output_file = os.path.join(output_dir, f'innovus_{stage_short}.tcl')
        with open(output_file, 'w') as f:
            f.write(final_content)

        generated_files.append(output_file)

        if verbose:
            print(f"Successfully generated {output_file}")

    # Print summary
    print(f"Successfully generated:")
    for file_path in generated_files:
        print(f"  - {file_path}")
    print(f"\nThe script has processed {len(all_proc_names)} proc(s) from {yaml_path}")

    return True

def main():
    """Main function when script is run directly"""
    import argparse

    parser = argparse.ArgumentParser(description='Generate TCL scripts for Innovus flow')
    parser.add_argument('-t', '--template-dir', default='template', help='Template directory')
    parser.add_argument('-f', '--flow-dir', default='flowRoot', help='Flow root directory')
    parser.add_argument('-o', '--output-dir', default='output', help='Output directory')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')

    args = parser.parse_args()

    success = generate_tcl_scripts(
        template_dir=args.template_dir,
        flow_dir=args.flow_dir,
        output_dir=args.output_dir,
        verbose=args.verbose
    )

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())