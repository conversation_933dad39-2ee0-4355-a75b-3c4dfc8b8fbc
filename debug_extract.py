#!/usr/bin/env python3
"""
调试提取函数
"""

import re
import os

def debug_extract_min_max_delay(file_path):
    """调试版本的提取函数"""
    print(f"Debugging file: {file_path}")
    print(f"File exists: {os.path.exists(file_path)}")
    
    if not os.path.exists(file_path):
        return None, None
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        print(f"Total lines: {len(lines)}")
        
        if len(lines) >= 2:
            print("Last 3 lines:")
            for i, line in enumerate(lines[-3:], len(lines)-2):
                print(f"  {i:2d}: {repr(line.rstrip())}")
            
            last_two_lines = lines[-2:]
            min_delay = None
            max_delay = None
            
            for i, line in enumerate(last_two_lines):
                line = line.strip()
                print(f"Processing line {i}: {repr(line)}")
                
                if 'Min element delay' in line:
                    print("  Found Min element delay line")
                    match = re.search(r'Min element delay\s*:\s*([0-9.]+)', line)
                    if match:
                        min_delay = match.group(1)
                        print(f"  Extracted min_delay: {min_delay}")
                    else:
                        print("  Failed to match regex")
                        
                elif 'Max element delay' in line:
                    print("  Found Max element delay line")
                    match = re.search(r'Max element delay\s*:\s*([0-9.]+)', line)
                    if match:
                        max_delay = match.group(1)
                        print(f"  Extracted max_delay: {max_delay}")
                    else:
                        print("  Failed to match regex")
            
            return min_delay, max_delay
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return None, None

if __name__ == "__main__":
    test_file = "sp_timing/funcgen3_ssg_0p81v_125_CMAX_setup/reports/dll_phy_top.write_adder_delay_00.rpt.ets"
    min_delay, max_delay = debug_extract_min_max_delay(test_file)
    print(f"\nFinal result:")
    print(f"Min delay: {min_delay}")
    print(f"Max delay: {max_delay}")
