#USAGE:setup_env $flow_version $stage $setup_tcl
#-- setup
#set flow_version <>TP-VERSION<>
#set stage        <>TP-FLOW_NAME<>
proc setup_env {flow_version stage setup_tcl} {
#set flow_version  a
#set stage a
source $setup_tcl
set DATA         Database/$stage
set RPT          Report/$stage
set OUTPUT       Output/$stage

mkdir -p $DATA
mkdir -p $RPT
mkdir -p $OUTPUT

setMultiCpuUsage -localCpu $multiCpuNum

redirect {report_resource -end ${top_module}_PreRun} >> ${RPT}/runtime_memory.rpt

#<>SFILE-prepare_env.tcl<>

#<>SFILE-global_setting.tcl<>

#<>SFILE-update_design.tcl<>

}