# Define step list (YAML format)
set stepList {
    innovus.floorplan {
        "setup_env $env_var1 $env_var2"
        "init_design $design_var1 $design_var2 $design_var3"
        "well_tap_insertion $tap_var1 $tap_var2"
        "save_design $save_var1"
        "report_design $report_var1 $report_var2"
    }
    innovus.place {
        "setup_env $env_var1 $env_var2"
        "restore_design $restore_var1"
        "config_design $config_var1 $config_var2"
        "update_timing $timing_var1"
        "place $place_var1 $place_var2"
        "save_design $save_var1"
        "report_design $report_var1 $report_var2"
    }
}