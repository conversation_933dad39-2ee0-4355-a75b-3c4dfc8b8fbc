proc innovus.place {args} {
    global stepList
    set steps [dict get $stepList innovus.place]
    
    # Set variables
    set env_var1 a
    set env_var2 a
    set restore_var1 a
    set config_var1 a
    set config_var2 a
    set timing_var1 a
    set place_var1 a
    set place_var2 a
    set save_var1 a
    set report_var1 a
    set report_var2 a
    
    # Parse arguments
    set start_idx 0
    set end_idx [expr [llength $steps] - 1]
    set has_range 0
    
    # Check if there's only one argument and it's a number
    if {[llength $args] == 1 && [string is integer [lindex $args 0]]} {
        set step_num [expr [lindex $args 0] - 1]
        if {$step_num >= 0 && $step_num < [llength $steps]} {
            set start_idx $step_num
            set end_idx $step_num
            set has_range 1
        } else {
            puts "Error: Step number out of range (1-[llength $steps])"
            return
        }
    } else {
        for {set i 0} {$i < [llength $args]} {incr i} {
            set arg [lindex $args $i]
            switch -exact $arg {
                "-from" {
                    set start_idx [expr [lindex $args [incr i]] - 1]
                    set has_range 1
                }
                "-to" {
                    set end_idx [expr [lindex $args [incr i]] - 1]
                    set has_range 1
                }
            }
        }
    }
    
    # Validate index range
    if {$start_idx < 0} {
        set start_idx 0
    }
    if {$end_idx >= [llength $steps]} {
        set end_idx [expr [llength $steps] - 1]
    }
    
    if {$has_range == 0} {
        puts "\n=== Place Stage Steps ==="
        puts "Usage:"
        puts "  innovus.place              # Show all steps"
        puts "  innovus.place 2            # Execute step 2 only"
        puts "  innovus.place -from 2      # Execute from step 2 to end"
        puts "  innovus.place -to 3        # Execute from start to step 3"
        puts "  innovus.place -from 2 -to 3 # Execute steps 2 to 3"
        puts "\nAvailable steps:"
        for {set i 0} {$i < [llength $steps]} {incr i} {
            puts "[expr $i + 1]. [lindex $steps $i]"
        }
        puts "\n"
        return
    }
    
    # Execute steps
    puts "Starting place stage"
    for {set i $start_idx} {$i <= $end_idx} {incr i} {
        set step [lindex $steps $i]
        puts "Executing: $step"
        eval $step
    }
    puts "Place stage completed"
}
