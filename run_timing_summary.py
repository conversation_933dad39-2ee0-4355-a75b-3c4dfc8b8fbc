#!/usr/bin/env python3
"""
运行 timing summary 生成器的示例脚本
"""

from timing_summary_generator import generate_timing_summary
import os

def main():
    # 检查 sp_timing 目录是否存在
    sp_timing_dir = 'sp_timing'
    
    if not os.path.exists(sp_timing_dir):
        print(f"Error: {sp_timing_dir} directory not found")
        print("Please make sure the sp_timing directory exists in the current path")
        return
    
    print("Starting timing summary generation...")
    print("=" * 50)
    
    # 生成 timing summary 报告
    generate_timing_summary(
        sp_timing_dir=sp_timing_dir,
        output_file='sp_timing_summary_generated.rpt'
    )
    
    print("=" * 50)
    print("Timing summary generation completed!")
    print("Output file: sp_timing_summary_generated.rpt")

if __name__ == "__main__":
    main()
