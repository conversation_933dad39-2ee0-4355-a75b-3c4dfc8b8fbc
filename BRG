#!/usr/bin/env python3
"""
BRG - Command Line Tool for Automated TCL Script Generation
"""

import os
import sys
import argparse

def setup_parser():
    """Setup command line argument parser"""
    parser = argparse.ArgumentParser(description='BRG - Command Line Tool for Automated TCL Script Generation')
    parser.add_argument('-p', '--place', action='store_true', help='Generate place TCL script')
    parser.add_argument('-o', '--output-dir', default='output', help='Output directory (default: output)')
    parser.add_argument('-t', '--template-dir', default=None, help='Template directory (default: current_dir/template)')
    parser.add_argument('-f', '--flow-dir', default=None, help='Flow root directory (default: current_dir/flowRoot)')
    parser.add_argument('-u', '--user-dir', default=None, help='User directory containing var.tcl file')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')

    return parser

def find_script_dir():
    """Find the directory containing the generate_tcl_script.py file"""
    # First try the directory where BRG is located
    brg_dir = os.path.dirname(os.path.abspath(__file__))
    if os.path.exists(os.path.join(brg_dir, 'generate_tcl_script.py')):
        return brg_dir

    # If not found, try the current working directory
    cwd = os.getcwd()
    if os.path.exists(os.path.join(cwd, 'generate_tcl_script.py')):
        return cwd

    # If still not found, search in PATH
    for path_dir in os.environ.get('PATH', '').split(os.pathsep):
        if not path_dir:
            continue
        try:
            candidate = os.path.abspath(path_dir)
            if os.path.exists(os.path.join(candidate, 'generate_tcl_script.py')):
                return candidate
        except:
            pass

    # If still not found, try the parent directory of BRG
    parent_dir = os.path.dirname(brg_dir)
    if os.path.exists(os.path.join(parent_dir, 'generate_tcl_script.py')):
        return parent_dir

    # Default to the directory where BRG is located
    return brg_dir

def generate_place_script(args):
    """Generate place TCL script"""
    # Find the script directory
    script_dir = find_script_dir()

    # Add script directory to Python path
    sys.path.append(script_dir)

    # Import the generate_tcl_script module
    try:
        # Try to import directly first
        try:
            from generate_tcl_script import generate_tcl_scripts
            if args.verbose:
                print("Successfully imported generate_tcl_script module")
        except ImportError:
            # If direct import fails, try to load the module from script_dir
            import imp
            module_path = os.path.join(script_dir, 'generate_tcl_script.py')
            if not os.path.exists(module_path):
                raise ImportError("Module file not found: " + module_path)

            if args.verbose:
                print("Loading module from:", module_path)

            # Load the module from file
            generate_tcl_script = imp.load_source('generate_tcl_script', module_path)
            generate_tcl_scripts = generate_tcl_script.generate_tcl_scripts

            if args.verbose:
                print("Successfully loaded generate_tcl_script module from file")
    except Exception as e:
        print("Error: Could not import generate_tcl_script.py")
        print("Error details:", str(e))
        print("Script directory:", script_dir)
        print("Current directory:", os.getcwd())
        print("Python path:", sys.path)
        print("Make sure generate_tcl_script.py exists in one of these locations:")
        print("  - Same directory as BRG:", os.path.dirname(os.path.abspath(__file__)))
        print("  - Current working directory:", os.getcwd())
        print("  - A directory in your PATH")
        return 1

    # Set default directories if not specified
    # Use script directory instead of current directory for default paths
    script_dir = os.path.dirname(os.path.abspath(__file__))
    template_dir = args.template_dir or os.path.join(script_dir, 'template')
    flow_dir = args.flow_dir or os.path.join(script_dir, 'flowRoot')

    # Create absolute paths
    template_dir = os.path.abspath(template_dir)
    flow_dir = os.path.abspath(flow_dir)
    output_dir = os.path.abspath(args.output_dir)

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    # Call the generate_place_script function
    if args.verbose:
        print("Script directory:", script_dir)
        print("Template directory:", template_dir)
        print("Flow directory:", flow_dir)
        print("Output directory:", output_dir)
        if args.user_dir:
            print("User directory:", args.user_dir)

    success = generate_tcl_scripts(
        template_dir=template_dir,
        flow_dir=flow_dir,
        output_dir=output_dir,
        verbose=args.verbose
    )

    if success:
        return 0
    else:
        print("Failed to generate TCL scripts")
        return 1

def main():
    """Main entry point"""
    parser = setup_parser()
    args = parser.parse_args()

    if args.place:
        return generate_place_script(args)
    else:
        parser.print_help()
        return 1

if __name__ == "__main__":
    sys.exit(main())
