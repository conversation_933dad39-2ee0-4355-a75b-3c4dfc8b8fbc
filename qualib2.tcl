#Minute:  55
#Second:  47

## create workspace ##
create_workspace ln04lpp_gpio_bump -overwrite

## create session(version and vendor) ##
create_session ln04lpp_gpio_bump_LN04LPP_GP
# No verilog view found

## link cdl ##

create_cdl_corner -session ln04lpp_gpio_bump_LN04LPP_GP corner_cdl_1
link_cdl -session ln04lpp_gpio_bump_LN04LPP_GP -corner corner_cdl_1 "/tech_1/designkit/Samsung/LN04LPP_GP/library/Foundry_IP/From_Fab/samsung/ln04lpp_gpio_bump/V1.03/cdl/ln04lpp_bump_lib.cdl"
## link gds ##

skip_virtual_top_cell -level 1
create_gds_corner -session ln04lpp_gpio_bump_LN04LPP_GP corner_gds_1
link_gds -session ln04lpp_gpio_bump_LN04LPP_GP -corner corner_gds_1 -tech /tech_1/designkit/Samsung/LN04LPP_GP/pdk/PDKLIB/pdk_virtuoso/latest/file/oa/ln04lpp_tech_14M_3Mx_2Fx_7Dx_2Iz_LB/ln04lpp_tech_14M_3Mx_2Fx_7Dx_2Iz_LB.tf
link_gds -session ln04lpp_gpio_bump_LN04LPP_GP -corner corner_gds_1 -tech /tech_1/designkit/Samsung/LN04LPP_GP/pdk/PDKLIB/pdk_virtuoso/latest/file/oa/ln04lpp_tech_14M_3Mx_2Fx_7Dx_2Iz_LB/ln04lpp_tech.layermap
link_gds -session ln04lpp_gpio_bump_LN04LPP_GP -corner corner_gds_1 "/tech_1/designkit/Samsung/LN04LPP_GP/library/Foundry_IP/From_Fab/samsung/ln04lpp_gpio_bump/V1.03/gds/ln04lpp_bump_lib.gds"

## link lef ##


create_lef_corner -session ln04lpp_gpio_bump_LN04LPP_GP corner_lef_tlef
link_lef -session ln04lpp_gpio_bump_LN04LPP_GP -corner corner_lef_tlef "/tech_1/designkit/Samsung/LN04LPP_GP/pdk/PNR/tf_innovus/V1.0.8.0/file/14M_3Mx_2Fx_7Dx_2Iz_LB/6p25TR/ln04lpp_tech_14M_3Mx_2Fx_7Dx_2Iz_LB_wo_stackedviarule.lef"

create_lef_corner -session ln04lpp_gpio_bump_LN04LPP_GP corner_lef_1
link_lef -session ln04lpp_gpio_bump_LN04LPP_GP -corner corner_lef_1 "/tech_1/designkit/Samsung/LN04LPP_GP/library/Foundry_IP/From_Fab/samsung/ln04lpp_gpio_bump/V1.03/lef/ln04lpp_bump_lib.flip.lef"

create_lef_corner -session ln04lpp_gpio_bump_LN04LPP_GP corner_lef_2
link_lef -session ln04lpp_gpio_bump_LN04LPP_GP -corner corner_lef_2 "/tech_1/designkit/Samsung/LN04LPP_GP/library/Foundry_IP/From_Fab/samsung/ln04lpp_gpio_bump/V1.03/lef/ln04lpp_bump_lib.wire.lef"
# No tmlib view found

### checking ##
show_empty_cells 1
### inspect

check -session ln04lpp_gpio_bump_LN04LPP_GP -view lef

scheck -session ln04lpp_gpio_bump_LN04LPP_GP -view lef

check -session ln04lpp_gpio_bump_LN04LPP_GP -view gds

check -session ln04lpp_gpio_bump_LN04LPP_GP -view cdl

set lefcor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view lef]
set gdscor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view gds]
foreach i $gdscor {
### checking ##
show_empty_cells 1
### inspect

check -session ln04lpp_gpio_bump_LN04LPP_GP -view lef

scheck -session ln04lpp_gpio_bump_LN04LPP_GP -view lef

check -session ln04lpp_gpio_bump_LN04LPP_GP -view gds

check -session ln04lpp_gpio_bump_LN04LPP_GP -view cdl

set lefcor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view lef]
set gdscor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view gds]
foreach i $gdscor {
    foreach k $lefcor {
        if { [regexp {_tlef} $k] } {
        } else {
            xcheck -session ln04lpp_gpio_bump_LN04LPP_GP -view_pair {gds lef} -corner_pair [list $i $k]
        }
    }
}

set cdlcor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view cdl]
set gdscor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view gds]
foreach i $cdlcor {
    foreach k $gdscor {
        xcheck -session ln04lpp_gpio_bump_LN04LPP_GP -view_pair {cdl gds} -corner_pair [list $i $k]
    }
}

set cdlcor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view cdl]
set lefcor [get_corners -session ln04lpp_gpio_bump_LN04LPP_GP -view lef]
foreach i $cdlcor {
    foreach k $lefcor {
        xcheck -session ln04lpp_gpio_bump_LN04LPP_GP -view_pair {cdl lef} -corner_pair [list $i $k]
    }
}

### report ##
report_inspection_result -dir ./ln04lpp_gpio_bump_html -format html -overwrite
report_inspection_result -dir ./ln04lpp_gpio_bump_text -format text -overwrite
