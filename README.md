# BRG - TCL Script Generator

BRG 是一个命令行工具，用于自动生成 Innovus 流程的 TCL 脚本。

## 功能

- 从 YAML 配置文件中读取 proc 名称列表
- 从 TCL 文件中提取 USAGE 命令
- 生成 TCL 脚本列表
- 结合模板生成最终的 TCL 脚本

## 安装

### 简单安装（Linux 环境）

1. 确保 BRG 脚本可执行：
   ```bash
   chmod +x BRG
   ```

2. 将 BRG 目录添加到 PATH 环境变量，在 `.cshrc` 文件中添加：
   ```bash
   setenv PATH "${PATH}:/path/to/BRG"
   ```

   请将 `/path/to/BRG` 替换为实际的 BRG 目录路径。

3. 重新加载 `.cshrc` 文件：
   ```bash
   source ~/.cshrc
   ```

## 使用方法

### 基本用法

生成 place TCL 脚本：

```bash
BRG -p
```

### 高级用法

指定目录：

```bash
BRG -p -t /path/to/templates -f /path/to/flow -o /path/to/output
```

启用详细输出：

```bash
BRG -p -v
```

### 命令行选项

```
-p, --place            生成 place TCL 脚本
-o, --output-dir DIR   指定输出目录（默认：当前目录/output）
-t, --template-dir DIR 指定模板目录（默认：当前目录/template）
-f, --flow-dir DIR     指定流程根目录（默认：当前目录/flowRoot）
-v, --verbose          启用详细输出
```

## 目录结构

BRG 工具假设以下目录结构：

```
工作目录/
├── template/
│   ├── step_list.yaml   # proc 名称列表
│   └── place_template   # place 模板
├── flowRoot/
│   └── Common/
│       └── STEP/
│           └── innovus/ # TCL 文件目录
└── output/              # 生成的 TCL 脚本
```

您可以使用命令行选项 `-t`, `-f`, `-o` 指定不同的目录。

## 配置

### step_list.yaml

```yaml
stepList:
  innovus.place:
    - innovus.setup_env
    - innovus.load_design
    # 添加更多 proc
```

## 故障排除

如果遇到问题：

1. 确保 Python 3 已安装
2. 检查目录结构和文件权限
3. 使用 `-v` 选项启用详细输出以获取更多信息
4. 确保 `generate_tcl_script.py` 文件与 `BRG` 文件在同一目录下
