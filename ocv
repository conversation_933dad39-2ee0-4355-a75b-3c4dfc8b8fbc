
        set v_derate_RVT [expr 4.2/100.000]
		set t_derate_RVT [expr 0.2/100.000]
        set base_derate_launch_cell [expr ${v_derate_RVT} + ${t_derate_RVT}]
		set base_derate_capture_cell  [expr ${v_derate_RVT} + ${t_derate_RVT}]
		set base_derate_data_cell  [expr ${v_derate_RVT} + ${t_derate_RVT}]

###### data 
        set cmd1 "set_timing_derate -increment -data -late -net_delay  [expr ${base_derate_data_net} + ${extra_derate}]" [get_cells $all_cell -quiet -filter "ref_name =~ *RVT*"]
    	set cmd2 "set_timing_derate -increment -data -late -cell_delay [expr ${base_derate_data_cell} + ${extra_derate}]" [get_cells $all_cell -quiet -filter "ref_name =~ *RVT*"]
		echo $cmd1 >> ${dir_logs}/${scenario}/ocv_setting.log
		echo $cmd2 >> ${dir_logs}/${scenario}/ocv_setting.log
		eval $cmd1
		eval $cmd2
###### launch clock
		
		set cmd1 "set_timing_derate -increment -clock -late -net_delay  [expr ${base_derate_launch_net} + ${extra_derate}]" [get_cells $all_cell -quiet -filter "ref_name =~ *RVT*"]
    	set cmd2 "set_timing_derate -increment -clock -late -cell_delay [expr ${base_derate_launch_cell} + ${extra_derate}]" [get_cells $all_cell -quiet -filter "ref_name =~ *RVT*"]

		echo $cmd1 >> ${dir_logs}/${scenario}/ocv_setting.log
		echo $cmd2 >> ${dir_logs}/${scenario}/ocv_setting.log
		eval $cmd1
		eval $cmd2
###### capture clock
	  	
	  	set cmd1 "set_timing_derate -increment -clock -early -net_delay  -[expr ${base_derate_capture_net} + ${extra_derate}]" [get_cells $all_cell -quiet -filter "ref_name =~ *RVT*"]
    	set cmd2 "set_timing_derate -increment -clock -early -cell_delay -[expr ${base_derate_capture_cell} + ${extra_derate} + ${v_derate} + ${t_derate}]" [get_cells $all_cell -quiet -filter "ref_name =~ *RVT*"]

		echo $cmd1 >> ${dir_logs}/${scenario}/ocv_setting.log
		echo $cmd2 >> ${dir_logs}/${scenario}/ocv_setting.log
		eval $cmd1
		eval $cmd2

