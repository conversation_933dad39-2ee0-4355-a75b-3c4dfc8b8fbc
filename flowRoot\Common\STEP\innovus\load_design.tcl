#USAGE:load_design $mmmc $database $setup_tcl
#-- Start to LoadDesign

proc load_design {mmmc database setup_tcl} {
global lef_list
#global top_module
source $setup_tcl
if {[file exists $mmmc]} {
    #restoreDesign  <>CP-DATABASE<>  <>CP-top_module<> -lef_files $lef_files -mmmcFile $mmmc
restoreDesign $database $top_module -lef_files $lef_list -mmmcFile $mmmc
#restoreDesign $database $top_module  -mmmcFile $mmmc
} else {
    #restoreDesign  <>CP-DATABASE<>  <>CP-top_module<> -lef_files $lef_files -noTiming
}
}


