#!/usr/bin/env python3
"""
测试提取函数是否正常工作
"""

from timing_summary_generator import extract_min_max_delay

def test_extraction():
    # 测试文件路径
    test_file = "sp_timing/funcgen3_ssg_0p81v_125_CMAX_setup/reports/dll_phy_top.write_adder_delay_00.rpt.ets"
    
    print(f"Testing extraction from: {test_file}")
    
    min_delay, max_delay = extract_min_max_delay(test_file)
    
    print(f"Min element delay: {min_delay}")
    print(f"Max element delay: {max_delay}")
    
    if min_delay and max_delay:
        print("✓ Extraction successful!")
    else:
        print("✗ Extraction failed!")
        
        # 让我们看看文件的最后几行
        try:
            with open(test_file, 'r') as f:
                lines = f.readlines()
            print("\nLast 5 lines of the file:")
            for i, line in enumerate(lines[-5:], len(lines)-4):
                print(f"{i:2d}: {line.rstrip()}")
        except Exception as e:
            print(f"Error reading file: {e}")

if __name__ == "__main__":
    test_extraction()
