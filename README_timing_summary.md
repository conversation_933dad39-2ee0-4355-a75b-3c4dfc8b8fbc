# Timing Summary Report Generator

这个工具用于自动扫描 `sp_timing` 目录下的所有 view，并生成格式化的 timing summary 报告。

## 功能特点

1. **自动扫描所有 view**：扫描 `sp_timing` 目录下的所有子目录作为 view
2. **提取 Min/Max delay**：从指定的报告文件中提取最后两行的 Min element delay 和 Max element delay
3. **提取 VIOLATION 信息**：从其他报告文件中提取所有以 VIOLATION 开头的行
4. **生成标准格式报告**：输出格式完全参考原始的 `sp_timing_summary.rpt` 文件

## 目录结构要求

```
sp_timing/
├── funcgen3_ssg_0p81v_125_CMAX_setup/
│   └── reports/
│       ├── dll_phy_top.read_adder_delay_00.rpt.ets
│       ├── dll_phy_top.read_adder_delay_01.rpt.ets
│       ├── ...
│       ├── dll_phy_top.read_adder_delay_07.rpt.ets
│       ├── dll_phy_top.master_phase_1_delay.rpt.ets
│       ├── dll_phy_top.read_delay.rpt.ets
│       ├── dll_phy_top.write_adder_delay_00.rpt.ets
│       ├── ...
│       ├── dll_phy_top.write_adder_delay_07.rpt.ets
│       ├── dll_phy_top.write_delay.rpt.ets
│       ├── dll_phy_top.write_dqs_delay.rpt.ets
│       ├── dll_phy_top.skew.addrcntrl.rpt.ets
│       ├── dll_phy_top.suhld.slice_0.rpt.ets
│       └── ... (其他报告文件)
├── funcgen3_ssg_0p81v_125_RCMAX_hold/
│   └── reports/
│       └── ... (相同的报告文件)
└── ... (其他 view 目录)
```

## 使用方法

### 方法1：直接运行
```bash
python3 timing_summary_generator.py
```

### 方法2：指定参数
```bash
python3 timing_summary_generator.py -d sp_timing -o my_summary.rpt -v
```

### 方法3：使用示例脚本
```bash
python3 run_timing_summary.py
```

### 方法4：作为模块导入
```python
from timing_summary_generator import generate_timing_summary

generate_timing_summary(
    sp_timing_dir='sp_timing',
    output_file='sp_timing_summary.rpt'
)
```

## 命令行参数

- `-d, --directory`: 指定 sp_timing 目录路径（默认：sp_timing）
- `-o, --output`: 指定输出报告文件名（默认：sp_timing_summary.rpt）
- `-v, --verbose`: 启用详细输出

## 处理的报告类型

### Min/Max Delay 报告（提取最后两行的数值）：
- `dll_phy_top.read_adder_delay_*.rpt.ets` (00-07)
- `dll_phy_top.master_phase_*_delay.rpt.ets`
- `dll_phy_top.read_delay.rpt.ets`
- `dll_phy_top.write_adder_delay_*.rpt.ets` (00-07)
- `dll_phy_top.write_delay.rpt.ets`
- `dll_phy_top.write_dqs_delay.rpt.ets`

### VIOLATION 报告（提取所有 VIOLATION 开头的行）：
- 所有其他的 `.rpt.ets` 文件

## 输出格式

生成的报告完全遵循原始 `sp_timing_summary.rpt` 的格式，包括：
- 分隔线和标题
- 表格格式的 Min/Max delay 信息
- 按 view 分组的 VIOLATION 信息

## 注意事项

1. 确保 `sp_timing` 目录存在且包含正确的子目录结构
2. 报告文件应该是 `.rpt.ets` 格式
3. 脚本会自动跳过不存在的文件
4. 如果无法提取数值，会显示 "N/A"
