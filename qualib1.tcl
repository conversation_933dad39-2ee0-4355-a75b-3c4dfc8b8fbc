#@ # Thu Mar 20 13:42:33 2025
create_workspace -overwrite /projects/taishan/analog/work/chenmiaomiao/VerificationData/qualib/202503201431
create_session run_202503201431 -vendor chenmiaomiao -version 1.0
link_verilog -session {run_202503201431} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/verilog/pma_s1t4r4.lvs.v.gz}
link_cdl -session {run_202503201431} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/cdl/pma_s1t4r4.spi}
link_gds -session {run_202503201431} -tech {/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.layermap}
link_gds -session {run_202503201431} -tech {/projects/taishan/analog/PDK/CDS/tsmcN12/tsmcN12.tf}
link_gds -session {run_202503201431} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/gds/pma_s1t4r4_FullPnR.gds}
link_lef -session {run_202503201431} {/tech_1/designkit/tsmc/n12ffcll/pdk/PNR/tf_innovus/latest/ori/tn12clpr001e1_1_2a/PRTF_Innovus_12nm_001_Cad_V12a/PRTF_Innovus_12nm_001_Cad_V12a/PR_tech/Cadence/LefHeader/Standard/VHV/PRTF_Innovus_N12_11M_2Xa1Xd3Xe2Y2R_UTRDL_H384_CPODE.12a.tlef /projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lef/pma_s1t4r4.lef.gz}
create_tmlib_corner -session {run_202503201431} {tt_0p8v_25c_typical_SETUP.merge}
link_tmlib -session {run_202503201431} -corner {tt_0p8v_25c_typical_SETUP.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_tt_0p8v_25c_typical_SETUP.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_m40c_rcworst_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_m40c_rcworst_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_m40c_rcworst_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_m40c_rcworstT_SETUP.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_m40c_rcworstT_SETUP.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_m40c_rcworstT_SETUP.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_m40c_cworst_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_m40c_cworst_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_m40c_cworst_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_m40c_cworstT_SETUP.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_m40c_cworstT_SETUP.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_m40c_cworstT_SETUP.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_125c_rcworst_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_125c_rcworst_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_125c_rcworst_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_125c_rcworstT_SETUP.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_125c_rcworstT_SETUP.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_125c_rcworstT_SETUP.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_125c_cworst_HOLD.merge}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_m40c_cworstT_SETUP.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_m40c_cworstT_SETUP.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_m40c_cworstT_SETUP.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_125c_rcworst_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_125c_rcworst_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_125c_rcworst_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_125c_rcworstT_SETUP.merge}
link_tmlib -session {run_202503201431} -corner {ssgnp_0p72v_125c_rcworstT_SETUP.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ssgnp_0p72v_125c_rcworstT_SETUP.merge.lib}
create_tmlib_corner -session {run_202503201431} {ssgnp_0p72v_125c_cworst_HOLD.merge}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_m40c_rcbest_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_m40c_rcbest_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_m40c_rcbest_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_m40c_cworst_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_m40c_cworst_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_m40c_cworst_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_m40c_cbest_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_m40c_cbest_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_m40c_cbest_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_125c_rcworst_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_125c_rcworst_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_125c_rcworst_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_125c_rcbest_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_125c_rcbest_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_125c_rcbest_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_125c_cworst_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_125c_cworst_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_125c_cworst_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_125c_cbest_HOLD.merge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_125c_cbest_HOLD.merge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_125c_cbest_HOLD.merge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_125c_cworst_HOLDmerge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_125c_cworst_HOLDmerge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_125c_cworst_HOLDmerge.lib}
create_tmlib_corner -session {run_202503201431} {ffgnp_0p88v_125c_cbest_HOLDmerge}
link_tmlib -session {run_202503201431} -corner {ffgnp_0p88v_125c_cbest_HOLDmerge} {/projects/taishan/be/handongyan/pma_s1t4r4/common/outputs/release/20240319/lib/pma_s1t4r4_ffgnp_0p88v_125c_cbest_HOLDmerge.lib}
source /usrhome/cad/setup/flow/qualib/rules/taishan.tcl
inspect -force -session run_202503201431
start_gui